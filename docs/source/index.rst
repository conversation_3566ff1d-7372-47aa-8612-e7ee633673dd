Welcome to TurdParty Documentation
===============================

.. image:: _static/logo.png
   :alt: TurdParty Logo
   :align: center
   :width: 200px

TurdParty is a comprehensive malware analysis platform for managing Vagrant virtual machines, file uploads, MinIO storage, advanced binary analysis using Frida dynamic instrumentation, and real-time eBPF monitoring with Inspektor Gadget integration.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   getting-started/index
   architecture/index
   security/index
   api/index
   development/index
   deployment/index
   troubleshooting/index
   user-guides/index
   reference/index
   changelog

Features
--------

- **Vagrant VM Management**: Create, manage, and monitor Vagrant virtual machines across 5 popular Linux distributions
- **File Upload & Storage**: Upload and manage files with MinIO storage and comprehensive metadata tracking
- **VM Injection**: Inject files into Vagrant VMs with real-time monitoring
- **Frida Binary Analysis**: Advanced dynamic binary analysis with real-time instrumentation and API hooking
- **Inspektor Gadget Integration**: Real-time eBPF-based system monitoring with ELK stack data streaming
- **Static Analysis**: Comprehensive file analysis for security, quality, and malware detection
- **Celery Task Processing**: Asynchronous task processing for file operations, VM lifecycle, and monitoring
- **Security Hardening**: Comprehensive security middleware with rate limiting, input validation, and secure headers
- **Docker Consolidation**: Unified Docker configuration with turdparty_ namespacing and standardized networking
- **API-First Architecture**: Comprehensive REST API with OpenAPI documentation and automated testing
- **Real-time Monitoring**: Live status monitoring with Cachet integration and health checks

About This Documentation
-----------------------

This documentation is built using Sphinx and includes:

- Installation and configuration guides
- Architecture documentation
- API reference
- Development guides
- Troubleshooting information
- User guides
- Reference materials

The documentation is available in HTML format for online viewing and PDF format for offline reference.

Getting Started
--------------

Check out the :doc:`getting-started/index` section for information on how to install and use TurdParty.

Development
----------

For developers, the :doc:`development/index` section provides information on contributing to the project,
testing procedures, and the development roadmap.

Architecture
-----------

The :doc:`architecture/index` section provides detailed information about the system architecture,
including Vagrant VM management, MinIO storage, and the FastAPI application infrastructure.

Indices and Tables
-----------------

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
