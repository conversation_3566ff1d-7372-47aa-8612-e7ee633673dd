services:
  diagnostic:
    container_name: turdparty_diagnostic
    build:
      context: ..
      dockerfile: .dockerwrapper/Dockerfile.diagnostic
    volumes:
      - ..:/app
      - ../tests:/app/tests:ro
    environment:
      - PYTHONPATH=/app
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - DOCKER_NETWORK=true
    depends_on:
      - postgres
      - minio
    networks:
      - turdparty_network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      
  postgres:
    image: postgres:14
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty_test
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - test_network
      
  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    command: server /data --console-address ":9001"
    volumes:
      - minio_data:/data
    networks:
      - test_network

networks:
  test_network:
    driver: bridge

volumes:
  postgres_data:
  minio_data: 